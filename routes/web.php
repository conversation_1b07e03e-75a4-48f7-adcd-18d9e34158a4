<?php

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
	Inertia::setRootView('public');
    return Inertia::render('Public/Welcome');
})->name('welcome');

// Tools Section
Route::prefix('tools')->name('tools.')->group(function () {
    Route::get('/pricing-calculator', function () {
        Inertia::setRootView('public');
        return Inertia::render('Tools/PricingCalculator');
    })->name('pricing-calculator');
});

Route::prefix('tools')->name('tools.')->group(function () {
    Route::get('/demo', function () {
        Inertia::setRootView('public');
        return Inertia::render('Tools/Demo');
    })->name('demo');
});

Route::get('/roadmap', function () {
	Inertia::setRootView('public');
    return Inertia::render('Public/Roadmap');
})->name('roadmap');

Route::get('/documentation', function () {
	Inertia::setRootView('public');
    return Inertia::render('Public/Documentation');
})->name('documentation');

Route::get('/terms-of-service', function () {
	Inertia::setRootView('public');
    return Inertia::render('Public/TermsOfServicePage');
})->name('terms-of-service');

Route::get('/privacy-policy', function () {
	Inertia::setRootView('public');
    return Inertia::render('Public/PrivacyPolicyPage');
})->name('privacy-policy');

Route::get('/refund-policy', function () {
	Inertia::setRootView('public');
    return Inertia::render('Public/RefundPolicyPage');
})->name('refund-policy');

// Blog routes (public)
Route::get('/blog', [\App\Http\Controllers\BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/{blog}', [\App\Http\Controllers\BlogController::class, 'show'])->name('blog.show');

// Sitemap route (public)
Route::get('/sitemap.xml', [\App\Http\Controllers\SitemapController::class, 'index'])->name('sitemap');


// Plan selection route (requires authentication)
Route::get('/plans', [\App\Http\Controllers\PlanController::class, 'index'])
    ->middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified'])
    ->name('plans.index');

Route::get('img/{filename}', function ($filename) {
    $path = public_path('images/' . $filename);
    if (!file_exists($path)) {
        abort(404);
    }
    return response()->file($path);
});

// Serve the widget script
Route::get('/widget/availability-search-v1.0.0.js', function () {
    $path = public_path('js/availability-search-v1.0.0.js');
    if (!file_exists($path)) {
        abort(404);
    }
    return response()->file($path, [
        'Content-Type' => 'application/javascript',
        'Cache-Control' => 'public, max-age=3600',
    ]);
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
    'ensure.plan',
    'ensure.accommodation.groups',
])->group(function () {
    Route::get('/dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/analytics', [\App\Http\Controllers\AnalyticsController::class, 'index'])->name('analytics');

    // Plan subscription route (requires authentication)
	Route::post('/plans/subscribe', [\App\Http\Controllers\PlanController::class, 'subscribe'])->name('plans.subscribe');

	// Promotion application route
	Route::post('/plans/apply-promotion', [\App\Http\Controllers\PlanController::class, 'applyPromotion'])->name('plans.apply-promotion');

    // Accommodation Group Routes
    Route::get('/accommodation-groups', [\App\Http\Controllers\AccommodationGroupsController::class, 'index'])->name('accommodation-groups.index');
    Route::get('/accommodation-groups/create', [\App\Http\Controllers\AccommodationGroupsController::class, 'create'])->name('accommodation-groups.create');
    Route::post('/accommodation-groups', [\App\Http\Controllers\AccommodationGroupsController::class, 'store'])->name('accommodation-groups.store');
    Route::get('/accommodation-groups/{group}', [\App\Http\Controllers\AccommodationGroupsController::class, 'show'])->name('accommodation-groups.show');
    Route::get('/accommodation-groups/{group}/edit', [\App\Http\Controllers\AccommodationGroupsController::class, 'edit'])->name('accommodation-groups.edit');
    Route::put('/accommodation-groups/{group}', [\App\Http\Controllers\AccommodationGroupsController::class, 'update'])->name('accommodation-groups.update');
    Route::delete('/accommodation-groups/{group}', [\App\Http\Controllers\AccommodationGroupsController::class, 'destroy'])->name('accommodation-groups.destroy');
    Route::post('/accommodation-groups/{group}/image', [\App\Http\Controllers\AccommodationGroupsController::class, 'uploadImage'])->name('accommodation-groups.image.upload');
    
    // Publish/Unpublish accommodation group
    Route::post('/accommodation-groups/{accommodationGroup}/publish', [\App\Http\Controllers\AccommodationGroupsController::class, 'publish'])->name('accommodation-groups.publish');
    Route::post('/accommodation-groups/{accommodationGroup}/unpublish', [\App\Http\Controllers\AccommodationGroupsController::class, 'unpublish'])->name('accommodation-groups.unpublish');

    // Accommodation Group Price Routes
    Route::get('/accommodation-groups/{group}/prices', [\App\Http\Controllers\API\AccommodationGroupPriceController::class, 'index'])->name('accommodation-groups.prices.index');
    Route::post('/accommodation-groups/{group}/prices', [\App\Http\Controllers\API\AccommodationGroupPriceController::class, 'store'])->name('accommodation-groups.prices.store');
    Route::put('/accommodation-groups/{group}/prices/{priceId}', [\App\Http\Controllers\API\AccommodationGroupPriceController::class, 'update'])->name('accommodation-groups.prices.update');
    Route::delete('/accommodation-groups/{group}/prices/{priceId}', [\App\Http\Controllers\API\AccommodationGroupPriceController::class, 'destroy'])->name('accommodation-groups.prices.destroy');
    Route::put('/accommodation-groups/{group}/prices/priorities', [\App\Http\Controllers\API\AccommodationGroupPriceController::class, 'updatePriorities'])->name('accommodation-groups.prices.priorities');

    // Accommodation Routes
    Route::get('/accommodations', [\App\Http\Controllers\AccommodationsController::class, 'index'])->name('accommodations.index');
    Route::get('/accommodations/create', [\App\Http\Controllers\AccommodationsController::class, 'create'])->name('accommodations.create');
    Route::post('/accommodations', [\App\Http\Controllers\AccommodationsController::class, 'store'])->name('accommodations.store');
    Route::get('/accommodations/{accommodation}', [\App\Http\Controllers\AccommodationsController::class, 'show'])->name('accommodations.show');
    Route::get('/accommodations/{accommodation}/edit', [\App\Http\Controllers\AccommodationsController::class, 'edit'])->name('accommodations.edit');
    Route::put('/accommodations/{accommodation}', [\App\Http\Controllers\AccommodationsController::class, 'update'])->name('accommodations.update');
    Route::delete('/accommodations/{accommodation}', [\App\Http\Controllers\AccommodationsController::class, 'destroy'])->name('accommodations.destroy');
    Route::put('/accommodations/{accommodation}/toggle-publish', [\App\Http\Controllers\AccommodationsController::class, 'togglePublish'])->name('accommodations.toggle-publish');
    Route::post('/accommodations/{accommodation}/images', [\App\Http\Controllers\AccommodationsController::class, 'uploadImages'])->name('accommodations.images.upload');
    Route::delete('/accommodations/{accommodation}/images/{mediaId}', [\App\Http\Controllers\AccommodationsController::class, 'deleteImage'])->name('accommodations.images.delete');

    // Price management routes
    Route::get('/accommodations/{accommodation}/prices', [\App\Http\Controllers\AccommodationsController::class, 'getPrices'])->name('accommodations.prices.index');
    Route::post('/accommodations/{accommodation}/prices', [\App\Http\Controllers\AccommodationsController::class, 'storePrice'])->name('accommodations.prices.store');
    Route::put('/accommodations/{accommodation}/prices/{priceId}', [\App\Http\Controllers\AccommodationsController::class, 'updatePrice'])->name('accommodations.prices.update');
    Route::delete('/accommodations/{accommodation}/prices/{priceId}', [\App\Http\Controllers\AccommodationsController::class, 'deletePrice'])->name('accommodations.prices.destroy');
    Route::put('/accommodations/{accommodation}/prices-priorities', [\App\Http\Controllers\AccommodationsController::class, 'updatePricePriorities'])->name('accommodations.prices.priorities');

    // Booking status management routes
    Route::get('/booking-statuses', [\App\Http\Controllers\AccommodationsController::class, 'getBookingStatuses'])->name('booking.statuses.index');
    Route::put('/accommodations/{accommodation}/bookings/{bookingId}/status', [\App\Http\Controllers\AccommodationsController::class, 'updateBookingStatus'])->name('accommodations.bookings.status.update');

    // Unavailable periods management routes
    Route::get('/accommodations/{accommodation}/unavailable-periods', [\App\Http\Controllers\AccommodationsController::class, 'getUnavailablePeriods'])->name('accommodations.unavailable-periods.index');
    Route::post('/accommodations/{accommodation}/unavailable-periods', [\App\Http\Controllers\AccommodationsController::class, 'storeUnavailablePeriod'])->name('accommodations.unavailable-periods.store');
    Route::delete('/accommodations/{accommodation}/unavailable-periods/{periodId}', [\App\Http\Controllers\AccommodationsController::class, 'deleteUnavailablePeriod'])->name('accommodations.unavailable-periods.destroy');

    // Discount management routes
    Route::post('/discounts', [\App\Http\Controllers\Api\DiscountController::class, 'store'])->name('discounts.store');
    Route::put('/discounts/{discount}', [\App\Http\Controllers\Api\DiscountController::class, 'update'])->name('discounts.update');
    Route::delete('/discounts/{discount}', [\App\Http\Controllers\Api\DiscountController::class, 'destroy'])->name('discounts.destroy');

    // Booking management routes
    Route::get('/bookings', [\App\Http\Controllers\BookingsController::class, 'index'])->name('bookings.index');
    Route::get('/bookings/{booking}', [\App\Http\Controllers\BookingsController::class, 'show'])->name('bookings.show');
    Route::get('/accommodations/{accommodation}/bookings/create', [\App\Http\Controllers\BookingsController::class, 'create'])->name('accommodations.bookings.create');
    Route::post('/accommodations/{accommodation}/bookings', [\App\Http\Controllers\BookingsController::class, 'store'])->name('accommodations.bookings.store');

    // Notification routes
    Route::get('/notifications', [\App\Http\Controllers\NotificationsController::class, 'index'])->name('notifications.index');
    Route::get('/notifications/api/get', [\App\Http\Controllers\NotificationsController::class, 'getNotificationsApi'])->name('notifications.api.get');
    Route::post('/notifications/{id}/mark-as-read', [\App\Http\Controllers\NotificationsController::class, 'markAsRead'])->name('notifications.mark-as-read');
    Route::post('/notifications/mark-all-as-read', [\App\Http\Controllers\NotificationsController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read');
    Route::delete('/notifications/{id}', [\App\Http\Controllers\NotificationsController::class, 'destroy'])->name('notifications.destroy');

    // Account routes
    Route::get('/account', [\App\Http\Controllers\AccountController::class, 'index'])->name('account.index');
    Route::get('/account/invoices/{invoiceId}', [\App\Http\Controllers\AccountController::class, 'downloadInvoice'])->name('account.invoices.download');
    Route::get('/account/payment', [\App\Http\Controllers\AccountController::class, 'showUpdatePayment'])->name('account.payment.edit');

    // User Settings routes
    Route::get('/settings', [\App\Http\Controllers\UserSettingsController::class, 'index'])->name('settings.index');
    Route::post('/settings', [\App\Http\Controllers\UserSettingsController::class, 'updateSettings'])->name('settings.update');
    Route::post('/settings/booking-terms', [\App\Http\Controllers\UserSettingsController::class, 'updateBookingTerms'])->name('settings.booking-terms.update'); // Legacy endpoint

    // Site management routes
    Route::get('/sites', [\App\Http\Controllers\SitesController::class, 'index'])->name('sites.index');
    Route::get('/sites/create', [\App\Http\Controllers\SitesController::class, 'create'])->name('sites.create');
    Route::post('/sites', [\App\Http\Controllers\SitesController::class, 'store'])->name('sites.store');
    Route::get('/sites/{site}', [\App\Http\Controllers\SitesController::class, 'show'])->name('sites.show');
    Route::get('/sites/{site}/edit', [\App\Http\Controllers\SitesController::class, 'edit'])->name('sites.edit');
    Route::put('/sites/{site}', [\App\Http\Controllers\SitesController::class, 'update'])->name('sites.update');
    Route::delete('/sites/{site}', [\App\Http\Controllers\SitesController::class, 'destroy'])->name('sites.destroy');
    Route::post('/sites/{site}/regenerate-api-key', [\App\Http\Controllers\SitesController::class, 'regenerateApiKey'])->name('sites.regenerate-api-key');
    Route::post('/sites/{site}/publish', [\App\Http\Controllers\SitesController::class, 'publish'])->name('sites.publish');
    Route::post('/sites/{site}/unpublish', [\App\Http\Controllers\SitesController::class, 'unpublish'])->name('sites.unpublish');
});


Route::get('preview-email', function () {
	$user = App\Models\User::first();
	$booking = App\Models\Booking::first();
	$notification = new App\Notifications\NewBookingNotification($booking);
	return $notification->toMail($user)->render();
});