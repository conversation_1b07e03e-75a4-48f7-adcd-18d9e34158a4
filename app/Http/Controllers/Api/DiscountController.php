<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Discount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DiscountController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'accommodation_id' => 'required|integer',
        ]);

        $discounts = Discount::where('discountable_id', $request->accommodation_id)
            ->where('discountable_type', 'App\Models\Accommodation')
            ->get();

        return response()->json($discounts);
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate($this->getValidationRules());

            $discount = new Discount($validated);
            $discount->discountable_id = $request->discountable_id;
            $discount->discountable_type = $request->discountable_type;
            $discount->save();

            return redirect()->back()->with('success', 'Discount created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to create discount. Please try again.');
        }
    }

    public function show(Discount $discount)
    {
        return response()->json($discount);
    }

    public function update(Request $request, Discount $discount)
    {
        try {
            $validated = $request->validate($this->getValidationRules());

            $discount->update($validated);

            return redirect()->back()->with('success', 'Discount updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update discount. Please try again.');
        }
    }

    public function destroy(Discount $discount)
    {
        try {
            $discount->delete();
            return redirect()->back()->with('success', 'Discount deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to delete discount. Please try again.');
        }
    }

    private function getValidationRules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'discountable_id' => 'required|integer',
            'discountable_type' => 'required|string|in:App\Models\Accommodation,App\Models\AccommodationGroup',
            'rule_type' => 'required|string|in:time_period,advance_booking,length_of_stay',
            'percentage' => 'required|numeric|min:0|max:100',
            'is_active' => 'boolean',
            'priority' => 'integer',
            'period_start_date' => 'nullable|date|required_if:rule_type,time_period',
            'period_end_date' => 'nullable|date|after_or_equal:period_start_date|required_if:rule_type,time_period',
            'min_days_in_advance' => 'nullable|integer|min:1|required_if:rule_type,advance_booking',
            'min_length_of_stay' => 'nullable|integer|min:1|required_if:rule_type,length_of_stay',
        ];
    }
}