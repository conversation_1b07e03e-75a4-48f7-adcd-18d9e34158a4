<template>
  <div>
    <div v-if="$page.props.flash.success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
      <span class="block sm:inline">{{ $page.props.flash.success }}</span>
    </div>
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Discounts</h3>
      <PrimaryButton @click="showCreateForm = true">Add Discount</PrimaryButton>
    </div>

    <div v-if="discounts.length === 0" class="text-center text-gray-500 dark:text-gray-400 py-6">
      <p>No discounts have been created for this accommodation yet.</p>
    </div>

    <div v-else class="space-y-4">
      <div v-for="discount in discounts" :key="discount.id" class="p-4 border rounded-lg flex justify-between items-center">
        <div>
          <p class="font-semibold">{{ discount.name }} - {{ discount.percentage }}%</p>
          <p class="text-sm text-gray-600">{{ formatRule(discount) }}</p>
        </div>
        <div class="flex items-center">
          <button @click="editDiscount(discount)" class="text-blue-600 hover:underline mr-4">Edit</button>
          <button @click="deleteDiscount(discount)" class="text-red-600 hover:underline">Delete</button>
        </div>
      </div>
    </div>

    <Modal :show="showCreateForm || showEditForm" @close="closeModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">{{ showEditForm ? 'Edit' : 'Create' }} Discount</h2>
        <DiscountForm :accommodation="accommodation" :discount="selectedDiscount" @close="closeModal" />
      </div>
    </Modal>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { usePage, router } from '@inertiajs/vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Modal from '@/Components/Modal.vue';
import DiscountForm from '@/Components/Discounts/DiscountForm.vue';

const props = defineProps({
  accommodation: Object,
  discounts: Array,
});

const showCreateForm = ref(false);
const showEditForm = ref(false);
const selectedDiscount = ref(null);

const editDiscount = (discount) => {
  selectedDiscount.value = discount;
  showEditForm.value = true;
};

const deleteDiscount = (discount) => {
  if (confirm('Are you sure you want to delete this discount?')) {
    router.delete(route('discounts.destroy', discount.id), {
	  onSuccess: () => {
        Inertia.reload({ only: ['discounts'] });
      },
    });
  }
};

const closeModal = () => {
  showCreateForm.value = false;
  showEditForm.value = false;
  selectedDiscount.value = null;
};

const formatRule = (discount) => {
  switch (discount.rule_type) {
    case 'time_period':
      return `Active from ${discount.period_start_date} to ${discount.period_end_date}`;
    case 'advance_booking':
      return `Book at least ${discount.min_days_in_advance} days in advance`;
    case 'length_of_stay':
      return `Stay for at least ${discount.min_length_of_stay} nights`;
    default:
      return 'Unknown rule';
  }
};
</script>
