<template>
  <form @submit.prevent="submit">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <InputLabel for="name" value="Name" />
        <TextInput id="name" v-model="form.name" type="text" class="mt-1 block w-full" required />
        <InputError :message="form.errors.name" class="mt-2" />
      </div>
      <div>
        <InputLabel for="percentage" value="Percentage" />
        <TextInput id="percentage" v-model="form.percentage" type="number" min="0" max="100" class="mt-1 block w-full" required />
        <InputError :message="form.errors.percentage" class="mt-2" />
      </div>
      <div>
        <InputLabel for="rule_type" value="Rule Type" />
        <select id="rule_type" v-model="form.rule_type" class="mt-1 block w-full p-3 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-accent-primary focus:border-accent-primary dark:focus:ring-accent-primary dark:focus:border-accent-primary sm:text-sm">
          <option value="time_period">Time Period</option>
          <option value="advance_booking">Advance Booking</option>
          <option value="length_of_stay">Length of Stay</option>
        </select>
        <InputError :message="form.errors.rule_type" class="mt-2" />
      </div>

      <div v-if="form.rule_type === 'time_period'" class="col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <InputLabel for="period_start_date" value="Start Date" />
          <TextInput id="period_start_date" v-model="form.period_start_date" type="date" class="mt-1 block w-full" />
          <InputError :message="form.errors.period_start_date" class="mt-2" />
        </div>
        <div>
          <InputLabel for="period_end_date" value="End Date" />
          <TextInput id="period_end_date" v-model="form.period_end_date" type="date" class="mt-1 block w-full" />
          <InputError :message="form.errors.period_end_date" class="mt-2" />
        </div>
      </div>

      <div v-if="form.rule_type === 'advance_booking'">
        <InputLabel for="min_days_in_advance" value="Minimum Days in Advance" />
        <TextInput id="min_days_in_advance" v-model="form.min_days_in_advance" type="number" min="1" class="mt-1 block w-full" />
        <InputError :message="form.errors.min_days_in_advance" class="mt-2" />
      </div>

      <div v-if="form.rule_type === 'length_of_stay'">
        <InputLabel for="min_length_of_stay" value="Minimum Length of Stay (nights)" />
        <TextInput id="min_length_of_stay" v-model="form.min_length_of_stay" type="number" min="1" class="mt-1 block w-full" />
        <InputError :message="form.errors.min_length_of_stay" class="mt-2" />
      </div>

      <div class="col-span-2">
        <div class="flex items-center">
          <input id="is_active" v-model="form.is_active" type="checkbox" class="h-4 w-4 text-accent-primary focus:ring-accent-primary border-gray-300 rounded" />
          <label for="is_active" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">Active</label>
        </div>
      </div>
    </div>

    <div class="flex items-center justify-end mt-6">
      <PrimaryButton type="button" @click="emit('close')" variant="light" class="mr-3">Cancel</PrimaryButton>
      <PrimaryButton :disabled="form.processing" :loading="form.processing">{{ discount ? 'Update' : 'Create' }} Discount</PrimaryButton>
    </div>
  </form>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

const props = defineProps({
  accommodation: Object,
  discount: Object,
});

const emit = defineEmits(['close']);

const form = useForm({
  name: props.discount?.name || '',
  percentage: props.discount?.percentage || '',
  rule_type: props.discount?.rule_type || 'time_period',
  period_start_date: props.discount?.period_start_date || '',
  period_end_date: props.discount?.period_end_date || '',
  min_days_in_advance: props.discount?.min_days_in_advance || '',
  min_length_of_stay: props.discount?.min_length_of_stay || '',
  is_active: props.discount?.is_active ?? true,
  discountable_id: props.accommodation.id,
  discountable_type: 'App\\Models\\Accommodation',
});

const submit = () => {
  if (props.discount) {
    form.put(route('discounts.update', props.discount.id), {
      onSuccess: () => {
        emit('close');
        form.reset();
      },
    });
  } else {
    form.post(route('discounts.store'), {
      onSuccess: () => {
        emit('close');
        form.reset();
      },
    });
  }
};
</script>
